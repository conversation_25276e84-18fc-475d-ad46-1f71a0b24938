apply plugin: 'idea'

idea.module {
    excludeDirs += file('out')
    resourceDirs += file('template')
    resourceDirs += file('scripts')
}

buildscript {
    repositories {
        // 阿里云镜像源
        maven { url 'https://maven.aliyun.com/repository/public/' }
        maven { url 'https://maven.aliyun.com/repository/google/' }
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin/' }
        // 备用源
        mavenCentral()
        google()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:7.4.2'
    }
}

allprojects {
    repositories {
        // 阿里云镜像源
        maven { url 'https://maven.aliyun.com/repository/public/' }
        maven { url 'https://maven.aliyun.com/repository/google/' }
        maven { url 'https://maven.aliyun.com/repository/jcenter/' }
        // 备用源
        mavenCentral()
        google()
    }
}

ext {
    minSdkVersion = 23
    targetSdkVersion = 32

    outDir = file("$rootDir/out")
}

task clean(type: Delete) {
    delete rootProject.buildDir, outDir
}
