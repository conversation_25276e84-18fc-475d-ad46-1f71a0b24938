# 国内源配置说明

本项目已成功配置国内镜像源，以提高依赖下载速度。

## 已配置的镜像源

### 1. Maven 仓库镜像源
在 `build.gradle` 和 `module/build.gradle` 中配置了阿里云镜像：

- **阿里云公共仓库**: `https://maven.aliyun.com/repository/public/`
- **阿里云Google仓库**: `https://maven.aliyun.com/repository/google/`
- **阿里云JCenter仓库**: `https://maven.aliyun.com/repository/jcenter/`
- **阿里云Gradle插件仓库**: `https://maven.aliyun.com/repository/gradle-plugin/`

### 2. Gradle 分发源
在 `gradle/wrapper/gradle-wrapper.properties` 中配置了腾讯云镜像：

- **腾讯云Gradle镜像**: `https://mirrors.cloud.tencent.com/gradle/gradle-7.5-bin.zip`

## 配置详情

### build.gradle 修改
```gradle
buildscript {
    repositories {
        // 阿里云镜像源
        maven { url 'https://maven.aliyun.com/repository/public/' }
        maven { url 'https://maven.aliyun.com/repository/google/' }
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin/' }
        // 备用源
        mavenCentral()
        google()
    }
}

allprojects {
    repositories {
        // 阿里云镜像源
        maven { url 'https://maven.aliyun.com/repository/public/' }
        maven { url 'https://maven.aliyun.com/repository/google/' }
        maven { url 'https://maven.aliyun.com/repository/jcenter/' }
        // 备用源
        mavenCentral()
        google()
    }
}
```

### module/build.gradle 修改
```gradle
repositories {
    // 阿里云镜像源
    maven { url 'https://maven.aliyun.com/repository/public/' }
    maven { url 'https://maven.aliyun.com/repository/google/' }
    // 备用源
    mavenLocal()
}
```

### gradle-wrapper.properties 修改
```properties
distributionUrl=https\://mirrors.cloud.tencent.com/gradle/gradle-7.5-bin.zip
```

## 验证结果

✅ Gradle 7.5 已成功从腾讯云镜像下载
✅ 项目构建配置已更新为使用国内镜像源
✅ 保留了原始源作为备用，确保稳定性

## 使用说明

现在可以正常使用以下命令进行项目构建：

```bash
# Windows
.\gradlew.bat build

# Linux/Mac
./gradlew build
```

所有依赖将优先从国内镜像源下载，大大提高下载速度。
